#!/usr/bin/env python3
"""
Script para continuar el entrenamiento del modelo TTS colombiano
Uso: python continue_training.py [--config CONFIG_FILE] [--epochs EPOCHS] [--lr LEARNING_RATE]
"""

import argparse
import json
import subprocess
import sys
from pathlib import Path

def load_config(config_path):
    """Cargar configuración desde archivo JSON"""
    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def save_config(config, config_path):
    """Guardar configuración a archivo JSON"""
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=4, ensure_ascii=False)

def update_config(config, args):
    """Actualizar configuración con argumentos de línea de comandos"""
    if args.epochs:
        config['num_train_epochs'] = args.epochs
    if args.learning_rate:
        config['learning_rate'] = args.learning_rate
    if args.batch_size:
        config['per_device_train_batch_size'] = args.batch_size
        config['per_device_eval_batch_size'] = args.batch_size
    if args.output_dir:
        config['output_dir'] = args.output_dir
    if args.hub_model_id:
        config['hub_model_id'] = args.hub_model_id
    
    return config

def run_training(config_path):
    """Ejecutar el entrenamiento usando accelerate"""
    cmd = ['accelerate', 'launch', 'run_vits_finetuning.py', config_path]
    
    print(f"🚀 Iniciando entrenamiento con configuración: {config_path}")
    print(f"📝 Comando: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print("✅ Entrenamiento completado exitosamente!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error durante el entrenamiento: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Continuar entrenamiento del modelo TTS colombiano")
    parser.add_argument('--config', default='training_config_examples/continue_colombian_training.json',
                       help='Archivo de configuración JSON')
    parser.add_argument('--epochs', type=int, help='Número de épocas de entrenamiento')
    parser.add_argument('--learning-rate', type=float, help='Tasa de aprendizaje')
    parser.add_argument('--batch-size', type=int, help='Tamaño del batch')
    parser.add_argument('--output-dir', help='Directorio de salida')
    parser.add_argument('--hub-model-id', help='ID del modelo en Hugging Face Hub')
    parser.add_argument('--dry-run', action='store_true', help='Solo mostrar configuración sin entrenar')
    
    args = parser.parse_args()
    
    # Verificar que el archivo de configuración existe
    config_path = Path(args.config)
    if not config_path.exists():
        print(f"❌ Error: Archivo de configuración no encontrado: {config_path}")
        sys.exit(1)
    
    # Cargar y actualizar configuración
    config = load_config(config_path)
    config = update_config(config, args)
    
    # Mostrar configuración
    print("📋 Configuración de entrenamiento:")
    print(f"   • Modelo base: {config['model_name_or_path']}")
    print(f"   • Dataset: {config['dataset_name']}")
    print(f"   • Épocas: {config['num_train_epochs']}")
    print(f"   • Learning rate: {config['learning_rate']}")
    print(f"   • Batch size: {config['per_device_train_batch_size']}")
    print(f"   • Directorio salida: {config['output_dir']}")
    
    if args.dry_run:
        print("🔍 Modo dry-run: No se ejecutará el entrenamiento")
        print("📄 Configuración completa:")
        print(json.dumps(config, indent=2, ensure_ascii=False))
        return
    
    # Guardar configuración actualizada si se modificó
    if any([args.epochs, args.learning_rate, args.batch_size, args.output_dir, args.hub_model_id]):
        temp_config_path = config_path.parent / f"temp_{config_path.name}"
        save_config(config, temp_config_path)
        config_path = temp_config_path
        print(f"💾 Configuración actualizada guardada en: {config_path}")
    
    # Ejecutar entrenamiento
    success = run_training(config_path)
    
    if success:
        print("🎉 ¡Entrenamiento completado!")
        print(f"📁 Modelo guardado en: {config['output_dir']}")
        if config.get('push_to_hub'):
            print(f"🤗 Modelo subido a Hub: {config['hub_model_id']}")
    else:
        print("💥 El entrenamiento falló. Revisa los logs para más detalles.")
        sys.exit(1)

if __name__ == "__main__":
    main()
