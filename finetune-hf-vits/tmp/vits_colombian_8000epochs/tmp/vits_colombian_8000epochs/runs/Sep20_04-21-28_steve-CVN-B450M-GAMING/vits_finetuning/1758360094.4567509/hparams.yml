accelerator_config: '{''split_batches'': False, ''dispatch_batches'': None, ''even_batches'':
  True, ''use_seedable_sampler'': True, ''non_blocking'': False, ''gradient_accumulation_kwargs'':
  None}'
adafactor: false
adam_beta1: 0.9
adam_beta2: 0.999
adam_epsilon: 1.0e-08
auto_find_batch_size: false
average_tokens_across_devices: false
batch_eval_metrics: false
bf16: false
bf16_full_eval: false
data_seed: None
dataloader_drop_last: false
dataloader_num_workers: 0
dataloader_persistent_workers: false
dataloader_pin_memory: true
dataloader_prefetch_factor: None
ddp_backend: None
ddp_broadcast_buffers: None
ddp_bucket_cap_mb: None
ddp_find_unused_parameters: None
ddp_timeout: 1800
debug: '[]'
deepspeed: None
disable_tqdm: false
do_eval: true
do_predict: false
do_step_schedule_per_epoch: true
do_train: true
eval_accumulation_steps: None
eval_batch_size: 4
eval_delay: 0
eval_do_concat_batches: true
eval_on_start: false
eval_steps: 500.0
eval_strategy: 'no'
eval_use_gather_object: false
fp16: true
fp16_backend: auto
fp16_full_eval: false
fp16_opt_level: O1
fsdp: '[]'
fsdp_config: '{''min_num_params'': 0, ''xla'': False, ''xla_fsdp_v2'': False, ''xla_fsdp_grad_ckpt'':
  False}'
fsdp_min_num_params: 0
fsdp_transformer_layer_cls_to_wrap: None
full_determinism: false
gradient_accumulation_steps: 2
gradient_checkpointing: false
gradient_checkpointing_kwargs: None
greater_is_better: None
group_by_length: false
half_precision_backend: auto
hub_always_push: false
hub_model_id: andrsvlz/mms-spa-colombian-8000epochs
hub_private_repo: None
hub_revision: None
hub_strategy: every_save
hub_token: <HUB_TOKEN>
ignore_data_skip: false
include_for_metrics: '[]'
include_inputs_for_metrics: false
include_num_input_tokens_seen: false
include_tokens_per_second: false
jit_mode_eval: false
label_names: None
label_smoothing_factor: 0.0
learning_rate: 5.0e-06
length_column_name: length
liger_kernel_config: None
load_best_model_at_end: false
local_rank: 0
log_level: passive
log_level_replica: warning
log_on_each_node: true
logging_dir: ./tmp/vits_colombian_8000epochs/runs/Sep20_04-21-28_steve-CVN-B450M-GAMING
logging_first_step: false
logging_nan_inf_filter: true
logging_steps: 100
logging_strategy: steps
lr_decay: 0.999875
lr_scheduler_kwargs: '{}'
lr_scheduler_type: cosine
max_grad_norm: 1.0
max_steps: 152000
metric_for_best_model: None
mp_parameters: ''
neftune_noise_alpha: None
no_cuda: false
num_train_epochs: 8000
optim: adamw_torch_fused
optim_args: None
optim_target_modules: None
output_dir: ./tmp/vits_colombian_8000epochs
overwrite_output_dir: true
parallelism_config: None
past_index: -1
per_device_eval_batch_size: 4
per_device_train_batch_size: 4
per_gpu_eval_batch_size: None
per_gpu_train_batch_size: None
prediction_loss_only: false
push_to_hub: true
push_to_hub_model_id: None
push_to_hub_organization: None
push_to_hub_token: <PUSH_TO_HUB_TOKEN>
ray_scope: last
remove_unused_columns: true
report_to: '[''tensorboard'']'
restore_callback_states_from_checkpoint: false
resume_from_checkpoint: None
run_name: None
save_on_each_node: false
save_only_model: false
save_safetensors: true
save_steps: 1000
save_strategy: steps
save_total_limit: 5
seed: 42
skip_memory_metrics: true
tf32: None
torch_compile: false
torch_compile_backend: None
torch_compile_mode: None
torch_empty_cache_steps: None
torchdynamo: None
tpu_metrics_debug: false
tpu_num_cores: None
train_batch_size: 4
use_cpu: false
use_ipex: false
use_legacy_prediction_loop: false
use_liger_kernel: false
use_mps_device: false
warmup_ratio: 0.05
warmup_steps: 0
weight_decay: 0.01
weight_disc: 3.0
weight_duration: 1.0
weight_fmaps: 1.0
weight_gen: 1.0
weight_kl: 1.5
weight_mel: 35.0
