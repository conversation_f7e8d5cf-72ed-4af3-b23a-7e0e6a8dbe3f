{"activation_dropout": 0.1, "architectures": ["VitsModelForPreTraining"], "attention_dropout": 0.1, "depth_separable_channels": 2, "depth_separable_num_layers": 3, "discriminator_kernel_size": 5, "discriminator_period_channels": [1, 32, 128, 512, 1024], "discriminator_periods": [2, 3, 5, 7, 11], "discriminator_scale_channels": [1, 16, 64, 256, 1024], "discriminator_stride": 3, "dtype": "float32", "duration_predictor_dropout": 0.5, "duration_predictor_filter_channels": 256, "duration_predictor_flow_bins": 10, "duration_predictor_kernel_size": 3, "duration_predictor_num_flows": 4, "duration_predictor_tail_bound": 5.0, "ffn_dim": 768, "ffn_kernel_size": 3, "flow_size": 192, "hidden_act": "relu", "hidden_dropout": 0.1, "hidden_size": 192, "hop_length": 256, "initializer_range": 0.02, "layer_norm_eps": 1e-05, "layerdrop": 0.1, "leaky_relu_slope": 0.1, "model_type": "vits", "noise_scale": 0.667, "noise_scale_duration": 0.8, "num_attention_heads": 2, "num_hidden_layers": 6, "num_speakers": 1, "posterior_encoder_num_wavenet_layers": 16, "prior_encoder_num_flows": 4, "prior_encoder_num_wavenet_layers": 4, "resblock_dilation_sizes": [[1, 3, 5], [1, 3, 5], [1, 3, 5]], "resblock_kernel_sizes": [3, 7, 11], "sampling_rate": 16000, "segment_size": 8192, "speaker_embedding_size": 0, "speaking_rate": 1.0, "spectrogram_bins": 513, "transformers_version": "4.56.1", "upsample_initial_channel": 512, "upsample_kernel_sizes": [16, 16, 4, 4], "upsample_rates": [8, 8, 2, 2], "use_bias": true, "use_stochastic_duration_prediction": true, "vocab_size": 45, "wavenet_dilation_rate": 1, "wavenet_dropout": 0.0, "wavenet_kernel_size": 5, "window_size": 4}