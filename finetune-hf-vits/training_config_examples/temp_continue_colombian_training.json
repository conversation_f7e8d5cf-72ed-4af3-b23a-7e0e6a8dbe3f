{"project_name": "mms_colombian_continued_training", "push_to_hub": true, "hub_model_id": "andrsvlz/mms-spa-colombian-continued", "report_to": ["tensorboard"], "overwrite_output_dir": true, "output_dir": "./tmp/vits_colombian_continued", "dataset_name": "ylacombe/google-colombian-spanish", "dataset_config_name": "female", "audio_column_name": "audio", "text_column_name": "text", "train_split_name": "train", "eval_split_name": "train", "speaker_id_column_name": "speaker_id", "override_speaker_embeddings": true, "filter_on_speaker_id": 2436, "full_generation_sample_text": "<PERSON><PERSON>, ¿cómo estás hoy? Espero que tengas un día maravilloso en Colombia.", "max_duration_in_seconds": 15, "min_duration_in_seconds": 1.0, "max_tokens_length": 200, "model_name_or_path": "ylacombe/mms-spa-finetuned-colombian-monospeaker", "preprocessing_num_workers": 4, "do_train": true, "num_train_epochs": 25, "gradient_accumulation_steps": 1, "gradient_checkpointing": false, "per_device_train_batch_size": 8, "learning_rate": 1e-05, "adam_beta1": 0.8, "adam_beta2": 0.99, "warmup_ratio": 0.01, "group_by_length": false, "do_eval": true, "eval_steps": 25, "per_device_eval_batch_size": 8, "max_eval_samples": 20, "do_step_schedule_per_epoch": true, "weight_disc": 3, "weight_fmaps": 1, "weight_gen": 1, "weight_kl": 1.5, "weight_duration": 1, "weight_mel": 35, "fp16": true, "seed": 42}