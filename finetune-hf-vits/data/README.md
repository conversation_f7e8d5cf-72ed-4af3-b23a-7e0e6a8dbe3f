# 📁 Estructura de Datos para Entrenamiento TTS

Esta carpeta contiene la estructura para agregar datos adicionales de audio en español colombiano.

## 📂 Estructura de Carpetas

```
data/
├── audio_samples/          # Archivos de audio (.wav, .mp3, .flac)
├── transcripts/           # Transcripciones de texto (.txt, .csv)
└── processed/            # Datos procesados (generado automáticamente)
```

## 🎵 Formato de Audio Recomendado

- **Formato**: WAV (16-bit, 22050 Hz o 16000 Hz)
- **Duración**: 1-15 segundos por muestra
- **Calidad**: Audio limpio, sin ruido de fondo
- **Acento**: Español colombiano preferiblemente

## 📝 Formato de Transcripciones

### Opción 1: Archivos individuales (.txt)
Cada archivo de audio debe tener su transcripción correspondiente:
```
audio_samples/sample001.wav
transcripts/sample001.txt
```

### Opción 2: Archivo CSV único
```csv
filename,text,speaker_id
sample001.wav,"<PERSON><PERSON>, ¿cómo estás hoy?",1
sample002.wav,"El clima en Bogotá está muy agradable",1
```

## 🔧 Scripts de Procesamiento

### Preparar datos personalizados:
```bash
python prepare_custom_data.py --audio_dir data/audio_samples --transcript_dir data/transcripts
```

### Validar calidad de audio:
```bash
python validate_audio.py --data_dir data/processed
```

## 📊 Recomendaciones para Mejores Resultados

1. **Cantidad mínima**: 50-100 muestras nuevas
2. **Consistencia**: Mismo hablante y calidad de grabación
3. **Diversidad**: Variedad de frases y contextos
4. **Duración**: Evitar muestras muy cortas (<1s) o muy largas (>15s)

## 🚀 Uso con el Entrenamiento

Una vez preparados los datos, actualiza la configuración:

```json
{
    "dataset_name": "./data/processed",
    "dataset_config_name": "custom",
    ...
}
```

## 📋 Checklist de Preparación

- [ ] Audio en formato WAV, 16-22kHz
- [ ] Transcripciones precisas y completas
- [ ] Nombres de archivos consistentes
- [ ] Audio sin ruido de fondo
- [ ] Duración apropiada (1-15s)
- [ ] Acento colombiano consistente
