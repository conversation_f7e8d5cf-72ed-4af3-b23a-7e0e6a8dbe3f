#!/usr/bin/env python3
"""
Script para preparar datos personalizados para entrenamiento TTS
Convierte audio y transcripciones en formato compatible con el entrenamiento
"""

import argparse
import json
import os
import pandas as pd
import soundfile as sf
import librosa
from pathlib import Path
from tqdm import tqdm

def validate_audio_file(audio_path, min_duration=1.0, max_duration=15.0, target_sr=22050):
    """Validar y obtener información de archivo de audio"""
    try:
        # Cargar audio
        audio, sr = librosa.load(audio_path, sr=target_sr)
        duration = len(audio) / sr
        
        # Validaciones
        if duration < min_duration:
            return None, f"Duración muy corta: {duration:.2f}s"
        if duration > max_duration:
            return None, f"Duración muy larga: {duration:.2f}s"
        
        # Información del audio
        info = {
            'path': str(audio_path),
            'duration': duration,
            'sample_rate': sr,
            'samples': len(audio)
        }
        
        return info, None
    except Exception as e:
        return None, f"Error procesando audio: {str(e)}"

def process_audio_files(audio_dir, output_dir, target_sr=22050):
    """Procesar archivos de audio y convertir a formato estándar"""
    audio_dir = Path(audio_dir)
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    audio_files = list(audio_dir.glob("*.wav")) + list(audio_dir.glob("*.mp3")) + list(audio_dir.glob("*.flac"))
    processed_files = []
    
    print(f"📁 Procesando {len(audio_files)} archivos de audio...")
    
    for audio_file in tqdm(audio_files):
        # Validar archivo
        info, error = validate_audio_file(audio_file, target_sr=target_sr)
        if error:
            print(f"⚠️  Saltando {audio_file.name}: {error}")
            continue
        
        # Cargar y procesar audio
        audio, sr = librosa.load(audio_file, sr=target_sr)
        
        # Normalizar audio
        audio = librosa.util.normalize(audio)
        
        # Guardar audio procesado
        output_file = output_dir / f"{audio_file.stem}.wav"
        sf.write(output_file, audio, sr)
        
        # Agregar información
        info['original_path'] = str(audio_file)
        info['processed_path'] = str(output_file)
        processed_files.append(info)
    
    print(f"✅ Procesados {len(processed_files)} archivos de audio")
    return processed_files

def load_transcripts(transcript_dir, audio_files):
    """Cargar transcripciones desde archivos .txt o .csv"""
    transcript_dir = Path(transcript_dir)
    transcripts = {}
    
    # Buscar archivo CSV primero
    csv_files = list(transcript_dir.glob("*.csv"))
    if csv_files:
        print(f"📄 Cargando transcripciones desde CSV: {csv_files[0]}")
        df = pd.read_csv(csv_files[0])
        for _, row in df.iterrows():
            filename = Path(row['filename']).stem
            transcripts[filename] = {
                'text': row['text'],
                'speaker_id': row.get('speaker_id', 1)
            }
    else:
        # Buscar archivos .txt individuales
        print("📄 Cargando transcripciones desde archivos .txt...")
        for audio_info in audio_files:
            audio_name = Path(audio_info['processed_path']).stem
            txt_file = transcript_dir / f"{audio_name}.txt"
            
            if txt_file.exists():
                with open(txt_file, 'r', encoding='utf-8') as f:
                    text = f.read().strip()
                transcripts[audio_name] = {
                    'text': text,
                    'speaker_id': 1
                }
            else:
                print(f"⚠️  No se encontró transcripción para: {audio_name}")
    
    print(f"✅ Cargadas {len(transcripts)} transcripciones")
    return transcripts

def create_dataset(audio_files, transcripts, output_dir):
    """Crear dataset en formato compatible con el entrenamiento"""
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    dataset_entries = []
    
    for audio_info in audio_files:
        audio_name = Path(audio_info['processed_path']).stem
        
        if audio_name in transcripts:
            entry = {
                'audio': audio_info['processed_path'],
                'text': transcripts[audio_name]['text'],
                'speaker_id': transcripts[audio_name]['speaker_id'],
                'duration': audio_info['duration']
            }
            dataset_entries.append(entry)
        else:
            print(f"⚠️  Sin transcripción para: {audio_name}")
    
    # Guardar dataset
    dataset_file = output_dir / "dataset.json"
    with open(dataset_file, 'w', encoding='utf-8') as f:
        json.dump(dataset_entries, f, indent=2, ensure_ascii=False)
    
    # Crear archivo CSV para compatibilidad
    df = pd.DataFrame(dataset_entries)
    csv_file = output_dir / "dataset.csv"
    df.to_csv(csv_file, index=False)
    
    print(f"💾 Dataset guardado:")
    print(f"   • JSON: {dataset_file}")
    print(f"   • CSV: {csv_file}")
    print(f"   • Entradas: {len(dataset_entries)}")
    
    return dataset_entries

def generate_stats(dataset_entries):
    """Generar estadísticas del dataset"""
    if not dataset_entries:
        return
    
    durations = [entry['duration'] for entry in dataset_entries]
    texts = [entry['text'] for entry in dataset_entries]
    
    print("\n📊 Estadísticas del Dataset:")
    print(f"   • Total de muestras: {len(dataset_entries)}")
    print(f"   • Duración promedio: {sum(durations)/len(durations):.2f}s")
    print(f"   • Duración mínima: {min(durations):.2f}s")
    print(f"   • Duración máxima: {max(durations):.2f}s")
    print(f"   • Duración total: {sum(durations)/60:.1f} minutos")
    print(f"   • Longitud promedio de texto: {sum(len(t) for t in texts)/len(texts):.1f} caracteres")

def main():
    parser = argparse.ArgumentParser(description="Preparar datos personalizados para entrenamiento TTS")
    parser.add_argument('--audio_dir', required=True, help='Directorio con archivos de audio')
    parser.add_argument('--transcript_dir', required=True, help='Directorio con transcripciones')
    parser.add_argument('--output_dir', default='data/processed', help='Directorio de salida')
    parser.add_argument('--target_sr', type=int, default=22050, help='Sample rate objetivo')
    parser.add_argument('--min_duration', type=float, default=1.0, help='Duración mínima en segundos')
    parser.add_argument('--max_duration', type=float, default=15.0, help='Duración máxima en segundos')
    
    args = parser.parse_args()
    
    print("🚀 Iniciando preparación de datos personalizados...")
    
    # Procesar archivos de audio
    audio_files = process_audio_files(
        args.audio_dir, 
        Path(args.output_dir) / "audio",
        target_sr=args.target_sr
    )
    
    if not audio_files:
        print("❌ No se procesaron archivos de audio válidos")
        return
    
    # Cargar transcripciones
    transcripts = load_transcripts(args.transcript_dir, audio_files)
    
    if not transcripts:
        print("❌ No se cargaron transcripciones")
        return
    
    # Crear dataset
    dataset_entries = create_dataset(audio_files, transcripts, args.output_dir)
    
    # Generar estadísticas
    generate_stats(dataset_entries)
    
    print("\n✅ ¡Preparación de datos completada!")
    print(f"📁 Datos listos en: {args.output_dir}")
    print("\n🔄 Próximos pasos:")
    print("1. Revisar las estadísticas del dataset")
    print("2. Actualizar la configuración de entrenamiento")
    print("3. Ejecutar: python continue_training.py")

if __name__ == "__main__":
    main()
