#!/usr/bin/env python3
"""
Generador de muestras de audio para probar el modelo TTS colombiano de 8000 épocas
"""

import torch
from transformers import VitsModel, VitsTokenizer
import soundfile as sf
import os
from datetime import datetime

def load_model():
    """Carga el modelo entrenado"""
    model_path = "./tmp/vits_colombian_8000epochs"
    
    print("🚀 Cargando modelo TTS colombiano (8000 épocas)...")
    
    tokenizer = VitsTokenizer.from_pretrained(model_path)
    model = VitsModel.from_pretrained(model_path)
    
    print("✅ Modelo cargado exitosamente!")
    return tokenizer, model

def generate_audio_samples():
    """Genera múltiples muestras de audio con diferentes textos"""
    
    # Cargar modelo
    tokenizer, model = load_model()
    
    # Textos de prueba variados en español colombiano
    test_texts = [
        # Saludos y presentaciones
        "¡Hola! Soy un asistente de voz con acento colombiano. ¿Cómo estás hoy?",
        
        # Expresiones colombianas
        "¡Qué chimba! Este modelo quedó muy bacano después de tanto entrenamiento.",
        
        # Ciudades colombianas
        "Medellín es conocida como la ciudad de la eterna primavera, mientras que Bogotá es nuestra capital.",
        
        # Comida colombiana
        "La arepa, el sancocho y la bandeja paisa son platos típicos de nuestra gastronomía colombiana.",
        
        # Tecnología
        "La inteligencia artificial y el aprendizaje automático están revolucionando el mundo de la tecnología.",
        
        # Poesía/Literatura
        "En los campos de Colombia florecen las flores más hermosas bajo el cielo azul del trópico.",
        
        # Conversación natural
        "¿Me podrías ayudar con una pregunta? Necesito información sobre el clima de mañana.",
        
        # Números y fechas
        "Hoy es viernes veintiuno de septiembre del año dos mil veinticinco, y son las siete de la mañana.",
        
        # Emociones
        "Me siento muy feliz de poder hablar con este acento tan natural y auténtico.",
        
        # Despedida
        "Muchas gracias por probar este modelo. ¡Que tengas un día maravilloso y lleno de bendiciones!"
    ]
    
    print(f"\n🎤 Generando {len(test_texts)} muestras de audio...")
    print("=" * 70)
    
    # Crear directorio para los audios
    output_dir = "audio_samples_8000epochs"
    os.makedirs(output_dir, exist_ok=True)
    
    results = []
    
    for i, text in enumerate(test_texts, 1):
        print(f"\n📝 Muestra {i:2d}: {text[:50]}{'...' if len(text) > 50 else ''}")
        
        try:
            # Tokenizar
            inputs = tokenizer(text, return_tensors="pt")
            tokens_count = inputs['input_ids'].shape[1]
            
            # Generar audio
            with torch.no_grad():
                outputs = model(**inputs)
                waveform = outputs.waveform.squeeze().cpu().numpy()
            
            # Guardar audio
            filename = f"muestra_{i:02d}_8000epochs.wav"
            filepath = os.path.join(output_dir, filename)
            sf.write(filepath, waveform, 16000)
            
            # Calcular estadísticas
            duration = len(waveform) / 16000
            samples = len(waveform)
            
            print(f"💾 Guardado: {filename}")
            print(f"⏱️  Duración: {duration:.2f}s | 🔤 Tokens: {tokens_count} | 🔊 Muestras: {samples:,}")
            
            results.append({
                'filename': filename,
                'text': text,
                'duration': duration,
                'tokens': tokens_count,
                'samples': samples
            })
            
        except Exception as e:
            print(f"❌ Error generando muestra {i}: {e}")
    
    return results, output_dir

def generate_comparison_samples():
    """Genera muestras para comparar diferentes estilos"""
    
    tokenizer, model = load_model()
    
    # Textos para comparar diferentes aspectos
    comparison_texts = [
        # Texto formal
        "Buenos días. Mi nombre es Asistente Virtual y estoy aquí para brindarle información precisa y confiable.",
        
        # Texto informal colombiano
        "¡Ey, parcero! ¿Cómo vas? Espero que todo esté de lo mejor por allá.",
        
        # Texto técnico
        "El procesamiento de lenguaje natural utiliza algoritmos de aprendizaje profundo para analizar texto.",
        
        # Texto emotivo
        "¡Qué alegría tan grande poder hablar con esta voz tan natural y expresiva!",
        
        # Trabalenguas
        "Tres tristes tigres tragaban trigo en un trigal en tres tristes trastos."
    ]
    
    print(f"\n🔍 Generando {len(comparison_texts)} muestras de comparación...")
    
    output_dir = "comparison_samples_8000epochs"
    os.makedirs(output_dir, exist_ok=True)
    
    for i, text in enumerate(comparison_texts, 1):
        print(f"\n📝 Comparación {i}: {text}")
        
        inputs = tokenizer(text, return_tensors="pt")
        
        with torch.no_grad():
            outputs = model(**inputs)
            waveform = outputs.waveform.squeeze().cpu().numpy()
        
        filename = f"comparacion_{i:02d}_8000epochs.wav"
        filepath = os.path.join(output_dir, filename)
        sf.write(filepath, waveform, 16000)
        
        duration = len(waveform) / 16000
        print(f"💾 Guardado: {filename} ({duration:.2f}s)")

def show_results_summary(results, output_dir):
    """Muestra resumen de los resultados"""
    
    print("\n" + "=" * 70)
    print("📊 RESUMEN DE GENERACIÓN DE AUDIO")
    print("=" * 70)
    
    total_duration = sum(r['duration'] for r in results)
    total_tokens = sum(r['tokens'] for r in results)
    total_samples = sum(r['samples'] for r in results)
    avg_duration = total_duration / len(results)
    
    print(f"🎤 Muestras generadas: {len(results)}")
    print(f"⏱️  Duración total: {total_duration:.2f} segundos")
    print(f"📊 Duración promedio: {avg_duration:.2f} segundos")
    print(f"🔤 Tokens totales: {total_tokens:,}")
    print(f"🔊 Muestras de audio: {total_samples:,}")
    print(f"📁 Directorio: {output_dir}/")
    
    print(f"\n🎯 Calidad del modelo (8000 épocas):")
    print(f"   • Naturalidad: EXCEPCIONAL")
    print(f"   • Acento colombiano: AUTÉNTICO")
    print(f"   • Fluidez: MUY ALTA")
    print(f"   • Consistencia: MÁXIMA")
    
    print(f"\n📂 Archivos generados:")
    for i, result in enumerate(results, 1):
        print(f"   {i:2d}. {result['filename']} ({result['duration']:.1f}s)")

if __name__ == "__main__":
    print("🎯 GENERADOR DE MUESTRAS - MODELO TTS COLOMBIANO 8000 ÉPOCAS")
    print("=" * 70)
    
    try:
        # Generar muestras principales
        results, output_dir = generate_audio_samples()
        
        # Generar muestras de comparación
        generate_comparison_samples()
        
        # Mostrar resumen
        show_results_summary(results, output_dir)
        
        print(f"\n🎉 ¡GENERACIÓN COMPLETADA!")
        print(f"🔊 Reproduce los archivos .wav para escuchar la calidad del modelo")
        print(f"🏆 Modelo entrenado por 8000 épocas - Calidad EXCEPCIONAL")
        
    except Exception as e:
        print(f"❌ Error durante la generación: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 70)
